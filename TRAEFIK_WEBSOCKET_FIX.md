# Traefik WebSocket Configuration Fix

## Port Configuration
- **8202**: Traefik Dashboard (`http://localhost:8202`)
- **8201**: Direct API access (`http://localhost:8201`)
- **80/443**: Domain-based access with <PERSON><PERSON> (`https://your-domain.com`)

## Problem Summary
The original Traefik configuration was causing WebSocket data exchange failures for the `/process-documents` endpoint because:

1. **No Sticky Sessions**: With 3 app replicas, WebSocket connections and HTTP requests could hit different instances
2. **Missing WebSocket Headers**: Traefik wasn't properly handling WebSocket upgrade requests
3. **Connection State Loss**: The `ConnectionManager` maintains connections in memory per instance, so cross-instance communication failed

## Solution Overview
The fix implements:
- **Sticky Sessions**: Ensures WebSocket and HTTP requests hit the same backend instance
- **WebSocket Headers**: Proper handling of WebSocket upgrade headers
- **Health Checks**: Better load balancer health monitoring
- **TLS Configuration**: Optimized for WebSocket connections

## Key Changes Made

### 1. Updated docker-compose.yml
- Added sticky session configuration with secure cookies
- Implemented WebSocket-specific middleware
- Added health check configuration
- Simplified Traefik command configuration

### 2. Created traefik/traefik.yml
- Centralized Traefik configuration
- Optimized TLS settings for WebSocket
- Better logging and monitoring setup

### 3. WebSocket-Specific Labels Added
```yaml
# Sticky sessions for WebSocket persistence
- "traefik.http.services.app.loadbalancer.sticky.cookie=true"
- "traefik.http.services.app.loadbalancer.sticky.cookie.name=traefik-app-session"

# WebSocket header middleware
- "traefik.http.middlewares.websocket-headers.headers.customrequestheaders.Connection=Upgrade"
- "traefik.http.middlewares.websocket-headers.headers.customrequestheaders.Upgrade=websocket"
```

## How It Fixes the WebSocket Issue

### Before (Broken):
1. Client connects to `/setup_files` WebSocket → hits App Instance A
2. Client makes POST to `/process-documents` → hits App Instance B
3. Instance B tries to send WebSocket updates but doesn't have the connection
4. Updates fail silently, no progress shown to client

### After (Fixed):
1. Client connects to `/setup_files` WebSocket → hits App Instance A, gets sticky cookie
2. Client makes POST to `/process-documents` → sticky cookie ensures it hits App Instance A
3. Instance A has the WebSocket connection and can send updates successfully
4. Client receives real-time progress updates as expected

## Files Modified/Created
- `docker-compose.yml` - Updated with WebSocket support
- `traefik/traefik.yml` - New centralized configuration
- `acme.json` - Created with proper permissions (600)
- `TRAEFIK_WEBSOCKET_FIX.md` - This documentation

## Testing and Verification Steps

### 1. Deploy the Updated Configuration
```bash
# Stop current services
docker-compose down

# Rebuild and start with new configuration
docker-compose up -d

# Check service status
docker-compose ps

# View Traefik logs
docker-compose logs traefik
```

### 2. Verify Traefik Dashboard
- Access: `http://your-domain:8202` or `http://localhost:8202`
- Check that all 3 app instances are healthy
- Verify sticky sessions are enabled in the service configuration

### 3. Test WebSocket Connection
```javascript
// Browser console test
const ws = new WebSocket('wss://your-domain/setup_files?token=YOUR_AUTH_TOKEN');
ws.onopen = () => console.log('WebSocket connected');
ws.onmessage = (event) => console.log('Received:', event.data);
ws.onerror = (error) => console.log('WebSocket error:', error);
```

### 4. Test Document Processing with WebSocket Updates
1. Connect to WebSocket endpoint: `/setup_files?token=YOUR_TOKEN`
2. Make POST request to `/process-documents` with test files
3. Verify you receive real-time status updates:
   - "Uploading Files": "In Progress"
   - "Extracting Text": "In Progress"
   - "Setting up Knowledge Base": "In Progress"
   - etc.

### 5. Verify Sticky Sessions
```bash
# Check cookies in browser developer tools
# Look for 'traefik-app-session' cookie
# Verify it's marked as Secure and HttpOnly
```

### 6. Monitor Application Logs
```bash
# Check app logs for WebSocket connections
docker-compose logs app | grep -i websocket

# Look for successful connection messages
docker-compose logs app | grep "Status update"
```

### 7. Load Test (Optional)
```bash
# Test multiple concurrent WebSocket connections
# Verify they maintain session affinity
# Check that all connections receive updates properly
```

## Troubleshooting

### If WebSocket Still Doesn't Work:
1. **Check Domain Configuration**: Ensure `${DOMAIN}` environment variable is set
2. **Verify SSL Certificates**: Check that ACME certificates are generated properly
3. **Review Traefik Logs**: Look for routing or middleware errors
4. **Test Without SSL**: Temporarily test with HTTP to isolate SSL issues

### Common Issues:
- **Cookie Not Set**: Check browser developer tools for the sticky session cookie
- **Connection Refused**: Verify all services are running and healthy
- **Updates Not Received**: Check that the same backend instance handles both WebSocket and HTTP requests

### Debug Commands:
```bash
# Check Traefik configuration
docker-compose exec traefik cat /etc/traefik/traefik.yml

# Test health endpoint
curl -k https://your-domain/health

# Check service discovery
docker-compose exec traefik traefik healthcheck
```
