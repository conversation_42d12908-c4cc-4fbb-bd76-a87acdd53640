version: '3.8'

services:
  traefik:
    image: traefik:v2.10
    command:
      - "--api.insecure=true"
      - "--api.dashboard=true"
      - "--providers.docker=true"
      - "--providers.docker.exposedbydefault=false"
      - "--providers.docker.watch=true"
      - "--entrypoints.web.address=:80"          # use default http port 80 for ACME http challenge
      - "--entrypoints.websecure.address=:443"
      - "--log.level=INFO"
      - "--accesslog=true"
      - "--accesslog.format=json"
      - "--accesslog.fields.names.ClientUsername=drop"
      - "--accesslog.fields.headers.names.Authorization=drop"
      - "--accesslog.fields.headers.names.Content-Type=keep"
      - "--metrics.prometheus=true"
      - "--ping=true"
      - "--global.sendAnonymousUsage=false"

      # Enable ACME and store certs in acme.json
      - "--certificatesresolvers.myresolver.acme.tlschallenge=true"   # TLS-ALPN challenge (better for HTTPS)
      - "--certificatesresolvers.myresolver.acme.email=<EMAIL>"  # <-- replace with your email
      - "--certificatesresolvers.myresolver.acme.storage=/acme/acme.json"
    ports:
      - "80:80"      # http port needed for ACME challenge
      - "443:443"    # https port
      - "8202:8080"  # dashboard
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./acme.json:/acme/acme.json   # <-- mount acme.json file
    networks:
      - traefik
    healthcheck:
      test: ["CMD", "traefik", "healthcheck", "--ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped

  app:
    image: eko-backend:latest
    networks:
      - traefik
    environment:
      - PORT=8000
      - WORKERS=1
      - SERVICE_NAME=eko-backend
    restart: unless-stopped
    deploy:
      replicas: 3
    extra_hosts:
    - minio.nextai.asia:*************
    labels:
      - "traefik.enable=true"

      # HTTP router on port 80 (redirect to https)
      - "traefik.http.routers.app.rule=Host(`${DOMAIN}`)"
      - "traefik.http.routers.app.entrypoints=web"
      - "traefik.http.routers.app.middlewares=https-redirect"

      # HTTPS router on 443 with automatic TLS
      - "traefik.http.routers.app-secure.rule=Host(`${DOMAIN}`)"
      - "traefik.http.routers.app-secure.entrypoints=websecure"
      - "traefik.http.routers.app-secure.tls=true"
      - "traefik.http.routers.app-secure.tls.certresolver=myresolver"
      - "traefik.http.routers.app-secure.service=app"
      - "traefik.http.services.app.loadbalancer.server.port=8000"

      # Middleware to redirect HTTP to HTTPS
      - "traefik.http.middlewares.https-redirect.redirectscheme.scheme=https"

networks:
  traefik:
    driver: bridge
