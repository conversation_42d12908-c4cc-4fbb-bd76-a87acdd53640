version: '3.8'

services:
  traefik:
    image: traefik:v2.10
    command:
      - "--configFile=/etc/traefik/traefik.yml"
    ports:
      - "80:80"      # http port needed for ACME challenge
      - "443:443"    # https port
      - "8201:8201"  # API instances port
      - "8202:8080"  # Traefik dashboard
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./acme.json:/acme/acme.json   # <-- mount acme.json file
      - ./traefik/traefik.yml:/etc/traefik/traefik.yml:ro  # <-- mount traefik config
    networks:
      - traefik
    healthcheck:
      test: ["CMD", "traefik", "healthcheck", "--ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped

  app:
    image: eko-backend:latest
    networks:
      - traefik
    environment:
      - PORT=8000
      - WORKERS=1
      - SERVICE_NAME=eko-backend
    restart: unless-stopped
    deploy:
      replicas: 3
    extra_hosts:
    - minio.nextai.asia:*************
    labels:
      - "traefik.enable=true"

      # HTTP router on port 80 (redirect to https)
      - "traefik.http.routers.app.rule=Host(`${DOMAIN}`)"
      - "traefik.http.routers.app.entrypoints=web"
      - "traefik.http.routers.app.middlewares=https-redirect"

      # HTTPS router on 443 with automatic TLS and WebSocket support
      - "traefik.http.routers.app-secure.rule=Host(`${DOMAIN}`)"
      - "traefik.http.routers.app-secure.entrypoints=websecure"
      - "traefik.http.routers.app-secure.tls=true"
      - "traefik.http.routers.app-secure.tls.certresolver=myresolver"
      - "traefik.http.routers.app-secure.service=app"

      # API router on port 8201 (direct access without domain)
      - "traefik.http.routers.app-api.rule=PathPrefix(`/`)"
      - "traefik.http.routers.app-api.entrypoints=api"
      - "traefik.http.routers.app-api.service=app"

      # WebSocket specific router for /setup_files endpoint
      - "traefik.http.routers.websocket.rule=Host(`${DOMAIN}`) && PathPrefix(`/setup_files`)"
      - "traefik.http.routers.websocket.entrypoints=websecure"
      - "traefik.http.routers.websocket.tls=true"
      - "traefik.http.routers.websocket.tls.certresolver=myresolver"
      - "traefik.http.routers.websocket.service=app"
      - "traefik.http.routers.websocket.middlewares=websocket-headers"

      # WebSocket router for port 8201
      - "traefik.http.routers.websocket-api.rule=PathPrefix(`/setup_files`)"
      - "traefik.http.routers.websocket-api.entrypoints=api"
      - "traefik.http.routers.websocket-api.service=app"
      - "traefik.http.routers.websocket-api.middlewares=websocket-headers"

      # Load balancer configuration with sticky sessions for WebSocket support
      - "traefik.http.services.app.loadbalancer.server.port=8000"
      - "traefik.http.services.app.loadbalancer.sticky.cookie=true"
      - "traefik.http.services.app.loadbalancer.sticky.cookie.name=traefik-app-session"
      - "traefik.http.services.app.loadbalancer.sticky.cookie.secure=true"
      - "traefik.http.services.app.loadbalancer.sticky.cookie.httpOnly=true"
      - "traefik.http.services.app.loadbalancer.sticky.cookie.sameSite=lax"

      # Health check configuration
      - "traefik.http.services.app.loadbalancer.healthcheck.path=/health"
      - "traefik.http.services.app.loadbalancer.healthcheck.interval=30s"
      - "traefik.http.services.app.loadbalancer.healthcheck.timeout=10s"

      # WebSocket-specific middleware for proper header handling
      - "traefik.http.middlewares.websocket-headers.headers.customrequestheaders.X-Forwarded-Proto=https"
      - "traefik.http.middlewares.websocket-headers.headers.customrequestheaders.X-Real-IP="
      - "traefik.http.middlewares.websocket-headers.headers.customresponseheaders.Access-Control-Allow-Origin=*"
      - "traefik.http.middlewares.websocket-headers.headers.customresponseheaders.Access-Control-Allow-Methods=GET,POST,OPTIONS,HEAD"
      - "traefik.http.middlewares.websocket-headers.headers.customresponseheaders.Access-Control-Allow-Headers=*"

      # Middleware to redirect HTTP to HTTPS
      - "traefik.http.middlewares.https-redirect.redirectscheme.scheme=https"

networks:
  traefik:
    driver: bridge
