#!/bin/bash

# 🚀 Production Deployment Script for Workload-Isolated Architecture

echo "🚀 Deploying Production Architecture..."
echo "   - 5 instances: High-priority webhooks"
echo "   - 3 instances: Document processing (session affinity)"
echo "   - 2 instances: General API"
echo ""

# Check if DOMAIN is set
if [ -z "$DOMAIN" ]; then
    echo "⚠️  Warning: DOMAIN environment variable not set"
    echo "   Set it with: export DOMAIN=your-domain.com"
    echo "   Or create a .env file with DOMAIN=your-domain.com"
    echo ""
fi

# Stop existing services
echo "🛑 Stopping existing services..."
docker-compose down

# Clean up old containers
echo "🧹 Cleaning up old containers..."
docker-compose rm -f

# Deploy production configuration
echo "🔨 Deploying production configuration..."
docker-compose -f docker-compose-production.yml up -d

# Wait for services to start
echo "⏳ Waiting for services to start..."
sleep 15

# Check service status
echo "📊 Service Status:"
docker-compose -f docker-compose-production.yml ps

echo ""
echo "🏥 Health Checks:"

# Check Traefik health
if curl -f http://localhost:8202/ping > /dev/null 2>&1; then
    echo "✅ Traefik is healthy"
else
    echo "❌ Traefik health check failed"
fi

# Check API health
if curl -f http://localhost:8201/health > /dev/null 2>&1; then
    echo "✅ API is responding"
else
    echo "❌ API health check failed"
fi

echo ""
echo "🌐 Access Information:"
echo "   Traefik Dashboard: http://localhost:8202"
echo "   Direct API Access: http://localhost:8201"
echo "   API Documentation: http://localhost:8201/docs"

if [ ! -z "$DOMAIN" ]; then
    echo "   Production API:    https://$DOMAIN"
    echo "   Production Docs:   https://$DOMAIN/docs"
fi

echo ""
echo "🎯 Route Distribution:"
echo "   Priority 1 (5 instances): /reply_generate, /sociar_webhook, /whatsapp_webhook"
echo "   Priority 2 (3 instances): /setup_files, /process-documents, /add-documents, /check_status"
echo "   Priority 3 (2 instances): Everything else"

echo ""
echo "🧪 Quick Tests:"
echo "   Test webhooks:  curl http://localhost:8201/reply_generate"
echo "   Test documents: curl http://localhost:8201/setup_files?token=test123"
echo "   Test general:   curl http://localhost:8201/health"

echo ""
echo "📈 Performance Monitoring:"
echo "   View Traefik stats: http://localhost:8202/dashboard/"
echo "   Check service logs: docker-compose -f docker-compose-production.yml logs [service-name]"

echo ""
echo "✅ Production deployment complete!"
echo "🚀 Your high-performance, workload-isolated architecture is ready!"
